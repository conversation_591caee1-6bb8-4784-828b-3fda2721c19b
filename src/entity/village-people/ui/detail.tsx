"use client"

import React, { useState } from "react"
import { VillagePeopleDetailModel } from "../api"
import { Card, CardHeader, CardTitle } from "@/shared/ui/card"
import { Button } from "@/shared/ui/button"
import { ArrowLeft, Phone, Plus, Vote, Loader2, DeleteIcon } from "lucide-react"
import Link from "next/link"
import { useVillagePersonVotes } from "../model/use-village-person-votes"
import { removeOwner } from "@/entity/votes/api/removeOwner"
import { toast } from "sonner"
import { useIsSuperAdmin } from "@/entity/users/hooks/use-is-super-admin"

type VillagePeopleDetailProps = {
    villagePerson: VillagePeopleDetailModel
}

export function VillagePeopleDetail({ villagePerson }: VillagePeopleDetailProps) {

    const isSuperAdmin = useIsSuperAdmin()

    // Используем кастомный хук для получения голосов
    const { votes, isLoading, refetch } = useVillagePersonVotes(villagePerson._id)

    const handleDelete = async (voteId: string) => {
        try {

            const conf = confirm("O'chirishni xohlaysizmi?")
            if (conf) {
                await removeOwner(voteId)
                toast("Muvaffaqqiyatli o'chirildi", {
                    description: "Ovoz muvaffaqiyatli o'chirildi"
                })
                await refetch()
            }

        } catch (e) {
            console.log(e)
        }
    }

    return (
        <div className="container mx-auto py-3 px-2 sm:px-4">
            <div className="flex justify-between items-center mb-3">
                <Link href="/dashboard/village-people">
                    <Button variant="outline" size="sm" className="flex items-center gap-1 cursor-pointer h-8">
                        <ArrowLeft className="h-3 w-3" />
                        {"Ro'yxatga qaytish"}
                    </Button>
                </Link>
            </div>

            <Card className="w-full mx-auto shadow-md">
                <CardHeader className="bg-gray-50 border-b py-3 px-4">
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle className="text-lg font-bold">{villagePerson.name} - {votes?.length} ta ovoz</CardTitle>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            <div className="mt-4">

                {isLoading ? (
                    <div className="bg-gray-100 rounded-lg p-6 text-center">
                        <Loader2 className="h-10 w-10 mx-auto text-gray-400 mb-3 animate-spin" />
                        <p className="text-gray-500 text-sm">{"Yuklanmoqda..."}</p>
                    </div>
                ) : votes.length === 0 ? (
                    <div className="bg-gray-100 rounded-lg p-6 text-center">
                        <Vote className="h-10 w-10 mx-auto text-gray-400 mb-3" />
                        <p className="text-gray-500 text-sm">{"Hozircha ovozlar yo'q"}</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                        {votes.map((vote) => (
                            <div key={vote._id} className="bg-white border rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
                                <div className="flex items-center gap-3">
                                    {vote.originalPhoneNumber && (
                                        <div className="relative bg-gray-100">
                                            {vote.originalPhoneNumber}
                                        </div>
                                    )}
                                    <div>
                                        {vote.phoneNumber && (
                                            <div className="flex items-center gap-1">
                                                <Phone className="h-3 w-3 text-gray-500" />
                                                <span className="text-sm font-medium">{vote.phoneNumber}</span>
                                            </div>
                                        )}
                                        {vote.voteDate && (
                                            <div className="text-xs text-gray-500">
                                                {vote.voteDate}
                                            </div>
                                        )}
                                    </div>
                                    {
                                        isSuperAdmin && (
                                            <Button onClick={() => handleDelete(vote._id)}>
                                                <DeleteIcon className="text-red" />
                                            </Button>
                                        )
                                    }
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    )
}
