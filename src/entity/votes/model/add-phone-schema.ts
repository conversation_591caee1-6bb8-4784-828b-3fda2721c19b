import { z } from "zod"

/**
 * Валидные коды операторов в Узбекистане
 */
const VALID_OPERATOR_CODES = [
  "33", "50", "55", "93", "94", "77", "99", "95", "87", "88", "97", "90", "91", "98", "20"
]

/**
 * Схема для формы добавления номера телефона
 */
export const addPhoneSchema = z.object({
  voteId: z.string()
    .min(1, { message: "<PERSON><PERSON><PERSON> tanlash kerak" }),
  phoneNumber: z.string()
    .length(3, { message: "Dastlabki 3 ta raqamni kiriting" })
    .refine((value) => {
      // Проверяем, что введенный код есть в списке валидных операторов
      return VALID_OPERATOR_CODES.includes(value.slice(0, 2))
    }, {
      message: "Bunaqa nomerli operator topilmadi"
    }),
  ownerId: z.string().optional(),
  updatedBy: z.string().optional()
})

export type AddPhoneFormValues = z.infer<typeof addPhoneSchema>
